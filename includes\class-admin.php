<?php
/**
 * Admin Class
 *
 * @package Q-Updater
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Q_Updater_Admin
{
    private $parent;

    /**
     * Constructor
     *
     * @param Q_Updater $parent Parent class instance
     */
    public function __construct($parent)
    {
        $this->parent = $parent;

        // Register AJAX handlers

        // Add GitHub API Debug page
        add_action('admin_menu', [$this, 'add_github_api_debug_page']);
    }

    /**
     * Add GitHub API Debug page to admin menu
     */
    public function add_github_api_debug_page()
    {
        add_submenu_page(
            'options-general.php',
            __('GitHub API Debug', 'q-updater'),
            __('GitHub API Debug', 'q-updater'),
            'manage_options',
            'q-updater-github-debug',
            [$this, 'render_github_api_debug_page']
        );
    }

    /**
     * Render GitHub API Debug page
     */
    public function render_github_api_debug_page()
    {
        require_once Q_UPDATER_PLUGIN_DIR . 'includes/admin/github-api-debug.php';
    }

    /**
     * Enqueue admin scripts and styles
     *
     * @param string $hook Current admin page
     */
    public function enqueue_scripts($hook)
    {
        // Load on Q-Updater settings page, plugins page, and GitHub API Debug page
        if ($hook != 'settings_page_q-updater' && $hook != 'plugins.php' && $hook != 'settings_page_q-updater-github-debug')
            return;

        wp_enqueue_style('q-updater-admin', plugins_url('css/admin.css', Q_UPDATER_PLUGIN_FILE), [], '1.0.1');
        wp_enqueue_script('q-updater-admin', plugins_url('js/admin.js', Q_UPDATER_PLUGIN_FILE), ['jquery'], '1.0.1', true);



        // Add Chart.js for analytics
        if ($hook == 'settings_page_q-updater') {
            // Only load Chart.js on the plugin's admin page
            wp_enqueue_script('qu-chartjs', 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js', [], '3.9.1', true);


        }

        // Get CSRF protection instance
        $csrf_protection = $this->parent->get_csrf_protection();

        // Create nonces with enhanced security
        wp_localize_script('q-updater-admin', 'qUpdater', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => $csrf_protection->create_nonce('q_updater_nonce', 3600), // 1 hour lifetime
            'bulk_update_nonce' => $csrf_protection->create_nonce('bulk_update_q_plugins', 3600), // 1 hour lifetime
            'dismiss_nonce' => $csrf_protection->create_nonce('q_updater_dismiss_notice', 86400), // 24 hour lifetime
            'settings_url' => admin_url('options-general.php?page=q-updater&tab=settings'),
            'review_submit_nonce' => $csrf_protection->create_nonce('qu_submit_review', 1800), // 30 minute lifetime
            'review_delete_nonce' => $csrf_protection->create_nonce('qu_delete_review', 1800), // 30 minute lifetime
            'analytics_nonce' => $csrf_protection->create_nonce('qu_analytics_nonce', 3600), // 1 hour lifetime
            'github_api_debug_nonce' => $csrf_protection->create_nonce('qu_github_api_debug', 3600), // 1 hour lifetime

            'github_debug_url' => admin_url('options-general.php?page=q-updater-github-debug'),
            'csrf_header' => 'X-CSRF-Token', // CSRF header name for AJAX requests
        ));
    }

    /**
     * Show update notices in admin
     */
    public function show_update_notices()
    {
        if (!current_user_can('update_plugins'))
            return;

        $updates = get_site_transient('update_plugins');
        if (empty($updates->response))
            return;

        foreach ($updates->response as $plugin_file => $plugin_data) {
            if (strpos($plugin_file, 'q-') === false)
                continue;

            $plugin_name = dirname($plugin_file);
            printf(
                '<div class="notice notice-warning"><p>' .
                /* translators: 1: plugin name, 2: version number, 3: update URL */
                __('A new version of <strong>%1$s</strong> is available (v%2$s). ', 'q-updater') .
                '<a href="%3$s">' . __('Update now', 'q-updater') . '</a>.' .
                '</p></div>',
                esc_html($plugin_name),
                esc_html($plugin_data->new_version),
                wp_nonce_url(self_admin_url('update.php?action=upgrade-plugin&plugin=' . $plugin_file), 'upgrade-plugin_' . $plugin_file)
            );
        }
    }


}
