<?php
/**
 * GitHub API Debug Admin Page
 *
 * This file contains the HTML and JavaScript for the GitHub API Debug admin page.
 * It provides a user interface for testing GitHub API connectivity, token validation,
 * and repository access.
 *
 * @package Q-Updater
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Get the GitHub API Debug instance
global $q_updater;

// Initialize variables with default values
$github_api_debug = null;
$token_manager = null;
$token_status = [
    'status' => 'error',
    'message' => __('Plugin components not properly initialized.', 'q-updater')
];

// Safely get components
if ($q_updater) {
    if (method_exists($q_updater, 'get_github_api_debug')) {
        $github_api_debug = $q_updater->get_github_api_debug();
    }

    if (method_exists($q_updater, 'get_token_manager')) {
        $token_manager = $q_updater->get_token_manager();

        // Only call test_token if token_manager is available
        if ($token_manager && method_exists($token_manager, 'test_token')) {
            $token_status = $token_manager->test_token();
        }
    }
}
?>

<div class="wrap qu-github-api-debug">
    <h1><?php _e('GitHub API Debug', 'q-updater'); ?></h1>

    <div class="qu-admin-notice-area"></div>

    <div class="qu-card">
        <div class="qu-card-header">
            <h2><?php _e('GitHub Token Status', 'q-updater'); ?></h2>
        </div>
        <div class="qu-card-body">
            <div class="qu-token-status">
                <?php if ($token_status['status'] === 'success'): ?>
                    <div class="qu-status-indicator success">
                        <span class="dashicons dashicons-yes-alt"></span>
                        <?php _e('Token Valid', 'q-updater'); ?>
                    </div>
                    <p><?php echo esc_html($token_status['message']); ?></p>
                <?php else: ?>
                    <div class="qu-status-indicator error">
                        <span class="dashicons dashicons-warning"></span>
                        <?php _e('Token Invalid', 'q-updater'); ?>
                    </div>
                    <p><?php echo esc_html($token_status['message']); ?></p>
                    <a href="<?php echo admin_url('options-general.php?page=q-updater&tab=settings'); ?>"
                        class="button button-primary">
                        <?php _e('Configure GitHub Token', 'q-updater'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="qu-card">
        <div class="qu-card-header">
            <h2><?php _e('Test GitHub API Connection', 'q-updater'); ?></h2>
        </div>
        <div class="qu-card-body">
            <form id="qu-test-github-api-form">
                <?php wp_nonce_field('qu_github_api_debug', 'nonce'); ?>

                <div class="qu-form-group">
                    <label for="qu-test-repo"><?php _e('Repository (optional)', 'q-updater'); ?></label>
                    <input type="text" id="qu-test-repo" name="repo" placeholder="username/repository"
                        class="regular-text">
                    <p class="description">
                        <?php _e('Enter a GitHub repository to test access (e.g., wordpress/wordpress)', 'q-updater'); ?>
                    </p>
                </div>

                <div class="qu-form-actions">
                    <button type="submit" class="button button-primary" id="qu-test-api-button">
                        <span class="dashicons dashicons-search"></span>
                        <?php _e('Test API Connection', 'q-updater'); ?>
                    </button>

                    <button type="button" class="button button-secondary" id="qu-clear-cache-button">
                        <span class="dashicons dashicons-trash"></span>
                        <?php _e('Clear GitHub Cache', 'q-updater'); ?>
                    </button>
                </div>
            </form>

            <div id="qu-test-results" class="qu-test-results" style="display: none;">
                <h3><?php _e('Test Results', 'q-updater'); ?></h3>
                <div class="qu-results-content"></div>
            </div>
        </div>
    </div>

    <div class="qu-card">
        <div class="qu-card-header">
            <h2><?php _e('Troubleshooting Steps', 'q-updater'); ?></h2>
        </div>
        <div class="qu-card-body">
            <ol class="qu-troubleshooting-steps">
                <li>
                    <strong><?php _e('Verify GitHub Token', 'q-updater'); ?></strong>
                    <p><?php _e('Ensure your GitHub token is valid and has the necessary permissions (repo scope).', 'q-updater'); ?>
                    </p>
                </li>
                <li>
                    <strong><?php _e('Check Repository Access', 'q-updater'); ?></strong>
                    <p><?php _e('Verify that the repository exists and is accessible with your GitHub account.', 'q-updater'); ?>
                    </p>
                </li>
                <li>
                    <strong><?php _e('Check Rate Limits', 'q-updater'); ?></strong>
                    <p><?php _e('GitHub API has rate limits. Without authentication, it\'s limited to 60 requests per hour.', 'q-updater'); ?>
                    </p>
                </li>
                <li>
                    <strong><?php _e('Clear Cache', 'q-updater'); ?></strong>
                    <p><?php _e('Use the "Clear GitHub Cache" button to remove cached API responses.', 'q-updater'); ?>
                    </p>
                </li>
                <li>
                    <strong><?php _e('Check Network Connectivity', 'q-updater'); ?></strong>
                    <p><?php _e('Ensure your server can connect to api.github.com.', 'q-updater'); ?></p>
                </li>
            </ol>
        </div>
    </div>
</div>

<script>
    jQuery(document).ready(function ($) {
        // Test GitHub API
        $('#qu-test-github-api-form').on('submit', function (e) {
            e.preventDefault();

            var $button = $('#qu-test-api-button');
            var $results = $('#qu-test-results');
            var $content = $results.find('.qu-results-content');

            $button.prop('disabled', true).addClass('qu-loading');
            $content.html('<div class="qu-loading-spinner"></div><p><?php _e('Testing GitHub API connection...', 'q-updater'); ?></p>');
            $results.show();

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'qu_test_github_api',
                    nonce: $('#nonce').val(),
                    repo: $('#qu-test-repo').val()
                },
                success: function (response) {
                    $button.prop('disabled', false).removeClass('qu-loading');

                    if (response.success) {
                        var data = response.data;
                        var html = '<div class="qu-test-summary">';

                        // Overall status
                        html += '<div class="qu-status-indicator ' + data.status + '">';
                        html += data.status === 'success' ?
                            '<span class="dashicons dashicons-yes-alt"></span>' :
                            '<span class="dashicons dashicons-warning"></span>';
                        html += data.status === 'success' ?
                            '<?php _e('All Tests Passed', 'q-updater'); ?>' :
                            '<?php _e('Some Tests Failed', 'q-updater'); ?>';
                        html += '</div>';

                        // Individual test results
                        html += '<div class="qu-test-details">';

                        // Token status
                        html += '<div class="qu-test-item">';
                        html += '<div class="qu-test-label"><?php _e('GitHub Token', 'q-updater'); ?></div>';
                        html += '<div class="qu-test-value ' + data.token_status + '">';
                        html += data.token_status === 'success' ?
                            '<span class="dashicons dashicons-yes-alt"></span>' :
                            '<span class="dashicons dashicons-warning"></span>';
                        html += data.messages[0];
                        html += '</div></div>';

                        // API status
                        html += '<div class="qu-test-item">';
                        html += '<div class="qu-test-label"><?php _e('API Connection', 'q-updater'); ?></div>';
                        html += '<div class="qu-test-value ' + data.api_status + '">';
                        html += data.api_status === 'success' ?
                            '<span class="dashicons dashicons-yes-alt"></span>' :
                            '<span class="dashicons dashicons-warning"></span>';
                        html += data.messages[1];
                        html += '</div></div>';

                        // Rate limit
                        html += '<div class="qu-test-item">';
                        html += '<div class="qu-test-label"><?php _e('Rate Limit', 'q-updater'); ?></div>';
                        html += '<div class="qu-test-value ' + data.rate_limit + '">';
                        html += data.rate_limit === 'success' ?
                            '<span class="dashicons dashicons-yes-alt"></span>' :
                            '<span class="dashicons dashicons-warning"></span>';
                        html += data.messages[2];
                        html += '</div></div>';

                        // Repository status (if provided)
                        if (data.repo_status !== 'unknown') {
                            html += '<div class="qu-test-item">';
                            html += '<div class="qu-test-label"><?php _e('Repository Access', 'q-updater'); ?></div>';
                            html += '<div class="qu-test-value ' + data.repo_status + '">';
                            html += data.repo_status === 'success' ?
                                '<span class="dashicons dashicons-yes-alt"></span>' :
                                '<span class="dashicons dashicons-warning"></span>';
                            html += data.messages[3];
                            html += '</div></div>';
                        }

                        html += '</div>'; // End test details
                        html += '</div>'; // End test summary

                        $content.html(html);
                    } else {
                        $content.html('<div class="qu-error-message">' + response.data + '</div>');
                    }
                },
                error: function () {
                    $button.prop('disabled', false).removeClass('qu-loading');
                    $content.html('<div class="qu-error-message"><?php _e('Error connecting to server. Please try again.', 'q-updater'); ?></div>');
                }
            });
        });

        // Clear GitHub Cache
        $('#qu-clear-cache-button').on('click', function () {
            var $button = $(this);
            var $noticeArea = $('.qu-admin-notice-area');

            $button.prop('disabled', true).addClass('qu-loading');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'qu_clear_github_cache',
                    nonce: $('#nonce').val()
                },
                success: function (response) {
                    $button.prop('disabled', false).removeClass('qu-loading');

                    if (response.success) {
                        $noticeArea.html('<div class="notice notice-success is-dismissible"><p>' + response.data.message + '</p></div>');
                    } else {
                        $noticeArea.html('<div class="notice notice-error is-dismissible"><p>' + response.data + '</p></div>');
                    }

                    // Auto-dismiss after 5 seconds
                    setTimeout(function () {
                        $noticeArea.find('.notice').fadeOut();
                    }, 5000);
                },
                error: function () {
                    $button.prop('disabled', false).removeClass('qu-loading');
                    $noticeArea.html('<div class="notice notice-error is-dismissible"><p><?php _e('Error connecting to server. Please try again.', 'q-updater'); ?></p></div>');
                }
            });
        });
    });
</script>

<style>
    .qu-card {
        background: #fff;
        border: 1px solid #ccd0d4;
        box-shadow: 0 1px 1px rgba(0, 0, 0, .04);
        margin-bottom: 20px;
        border-radius: 4px;
    }

    .qu-card-header {
        border-bottom: 1px solid #ccd0d4;
        padding: 12px 15px;
        background: #f8f9fa;
    }

    .qu-card-header h2 {
        margin: 0;
        font-size: 16px;
    }

    .qu-card-body {
        padding: 15px;
    }

    .qu-form-group {
        margin-bottom: 15px;
    }

    .qu-form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .qu-form-actions {
        margin-top: 20px;
    }

    .qu-form-actions .button {
        margin-right: 10px;
    }

    .qu-form-actions .dashicons {
        margin-top: 3px;
        margin-right: 5px;
    }

    .qu-test-results {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .qu-loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 10px;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .qu-status-indicator {
        display: inline-flex;
        align-items: center;
        padding: 5px 10px;
        border-radius: 4px;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .qu-status-indicator.success {
        background-color: #d4edda;
        color: #155724;
    }

    .qu-status-indicator.error,
    .qu-status-indicator.warning {
        background-color: #f8d7da;
        color: #721c24;
    }

    .qu-status-indicator .dashicons {
        margin-right: 5px;
    }

    .qu-test-details {
        margin-top: 15px;
    }

    .qu-test-item {
        display: flex;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #f0f0f0;
    }

    .qu-test-label {
        flex: 0 0 150px;
        font-weight: 600;
    }

    .qu-test-value {
        flex: 1;
        display: flex;
        align-items: flex-start;
    }

    .qu-test-value .dashicons {
        margin-right: 5px;
        margin-top: 2px;
    }

    .qu-test-value.success {
        color: #155724;
    }

    .qu-test-value.error,
    .qu-test-value.warning {
        color: #721c24;
    }

    .qu-troubleshooting-steps li {
        margin-bottom: 15px;
    }

    .qu-troubleshooting-steps strong {
        display: block;
        margin-bottom: 5px;
    }

    .qu-token-status {
        margin-bottom: 15px;
    }

    .qu-admin-notice-area {
        margin: 15px 0;
    }

    .qu-loading:after {
        content: "";
        display: inline-block;
        width: 12px;
        height: 12px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid #fff;
        border-radius: 50%;
        margin-left: 10px;
        animation: spin 1s linear infinite;
    }
</style>